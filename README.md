# 🏛️ Taluk Office Digital File Management System

A professional, government-grade digital file management system designed specifically for Taluk Offices in Karnataka, India. This system provides modern file tracking, QR code-based document management, and comprehensive administrative tools.

## 🌟 Features

### 📋 Core Functionality
- **Digital File Tracking** - QR code-based document identification and tracking
- **Smart Search System** - Advanced search by reference number, date, or keywords
- **Secure Access Control** - Role-based permissions for different user types
- **Digital Archive** - Comprehensive document storage with backup capabilities
- **Workflow Management** - Document approval and routing system
- **Compliance & Reporting** - Generate audit trails and compliance reports

### 🔐 User Roles
- **Administrator** - Full system access and user management
- **Officer** - Document management and approval workflows
- **Clerk** - Basic document entry and retrieval

### 🎨 Professional UI
- Government of Karnataka branding
- Mobile-responsive design
- Professional color scheme
- Accessibility compliant
- Multi-language support ready

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- MySQL 8.0+ (or SQLite for development)
- Modern web browser

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd taluk-office-system
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup Database**
   
   **Option A: MySQL (Recommended for Production)**
   ```bash
   python setup_mysql.py
   ```
   
   **Option B: SQLite (Development)**
   ```bash
   # Database will be created automatically
   ```

4. **Run the application**
   ```bash
   python app.py
   ```

5. **Access the system**
   - Open: http://127.0.0.1:5000
   - Login with default credentials (see below)

## 👥 Default Login Credentials

| Role | Username | Password | Description |
|------|----------|----------|-------------|
| Administrator | `admin` | `admin123` | Full system access |
| Officer | `officer` | `officer123` | Document management |
| Clerk | `clerk` | `clerk123` | Basic operations |

⚠️ **Important**: Change these passwords in production!

## 📁 Project Structure

```
taluk-office-system/
├── app.py                 # Main application file
├── config.py             # Configuration settings
├── requirements.txt      # Python dependencies
├── setup_mysql.py       # MySQL setup script
├── models/              # Database models
│   ├── user.py
│   ├── file.py
│   ├── location.py
│   └── access_log.py
├── templates/           # HTML templates
│   ├── base.html
│   ├── login.html
│   ├── dashboard.html
│   └── ...
├── static/             # Static assets
│   ├── css/
│   ├── js/
│   └── images/
└── utils/              # Utility functions
    ├── qr_generator.py
    └── file_tracker.py
```

## 🔧 Configuration

### Environment Variables (.env)
```env
SECRET_KEY=your-secret-key-here
DATABASE_URL=mysql+pymysql://user:password@localhost/taluk_office_db
```

### Database Configuration
- **Development**: SQLite (automatic)
- **Production**: MySQL 8.0+ (recommended)

## 📱 Features Overview

### 🏠 Dashboard
- Real-time statistics
- Recent document activity
- Quick action buttons
- User role-specific views

### 📄 Document Management
- Upload documents with metadata
- Generate QR codes automatically
- Track document location
- Access history logging

### 🔍 QR Code Scanner
- Camera-based scanning
- Manual QR code input
- Instant document retrieval
- Mobile-optimized interface

### 📊 Reports & Analytics
- Document access statistics
- User activity reports
- Compliance audit trails
- Export capabilities

### 👥 User Management
- Role-based access control
- User activity monitoring
- Permission management
- Audit logging

## 🛡️ Security Features

- **Authentication**: Secure password hashing
- **Authorization**: Role-based access control
- **Audit Trail**: Complete activity logging
- **Data Protection**: Encrypted sensitive data
- **Session Management**: Secure session handling

## 🌐 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 📞 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Phone**: 1800-XXX-XXXX
- **Documentation**: [Internal Wiki]

## 📄 License

This software is developed for Government of Karnataka.
All rights reserved.

## 🤝 Contributing

This is a government project. For contributions or modifications, please contact the IT department.

---

**Government of Karnataka - Digital India Initiative**
*Transforming governance through technology*
