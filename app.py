
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_from_directory
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
import qrcode
from datetime import datetime, timedelta
import uuid
from config import Config

import pymysql
pymysql.install_as_MySQLdb()
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_socketio import SocketIO, emit


# Import extensions
from extensions import db, login_manager, socketio

# Import Flask-Login components
from flask_login import login_required, current_user, login_user, logout_user
# SocketIO already imported above


# Initialize Flask app
app = Flask(__name__)
app.config.from_object(Config)

# Ensure upload directories exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['QR_CODE_FOLDER'], exist_ok=True)

# Initialize extensions with app
db.init_app(app)
login_manager.init_app(app)
socketio.init_app(app)

# Import models
from models.user import User
from models.file import File
from models.location import Location
from models.access_log import AccessLog

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
def index():
    return render_template('base.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            return redirect(url_for('dashboard'))

        flash('Invalid username or password')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    files = File.query.filter_by(user_id=current_user.id).all()
    return render_template('dashboard.html', files=files)

@app.route('/files/add', methods=['GET', 'POST'])
@login_required
def add_file():
    if request.method == 'POST':
        # Get form data
        title = request.form.get('title')
        description = request.form.get('description')
        rack_number = request.form.get('rack_number')
        row_number = request.form.get('row_number')
        position = request.form.get('position')

        # Handle file upload
        if 'file' not in request.files:
            flash('No file part')
            return redirect(request.url)

        file = request.files['file']

        if file.filename == '':
            flash('No selected file')
            return redirect(request.url)

        if file:
            # Generate a unique filename
            filename = secure_filename(file.filename)
            unique_filename = f"{uuid.uuid4()}_{filename}"
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
            file.save(file_path)

            # Create location record
            location = Location(rack_number=rack_number, row_number=row_number, position=position)
            db.session.add(location)
            db.session.flush()  # Get the location ID without committing

            # Create file record
            new_file = File(
                title=title,
                description=description,
                filename=unique_filename,
                original_filename=filename,
                location_id=location.id,
                user_id=current_user.id
            )
            db.session.add(new_file)
            db.session.flush()  # Get the file ID without committing

            # Generate QR code with JSON data
            qr_data = {
                'file_id': new_file.id,
                'title': title,
                'description': description,
                'location': f"Rack: {rack_number}, Row: {row_number}, Position: {position}",
                'created_at': new_file.created_at.strftime('%Y-%m-%d %H:%M:%S')
            }

            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            # Convert to JSON string for QR code
            import json
            qr.add_data(json.dumps(qr_data))
            qr.make(fit=True)

            qr_img = qr.make_image(fill_color="black", back_color="white")
            qr_filename = f"qr_{new_file.id}.png"
            qr_path = os.path.join(app.config['QR_CODE_FOLDER'], qr_filename)
            qr_img.save(qr_path)

            # Update file with QR code path
            new_file.qr_code = qr_filename

            # Log the file access
            access_log = AccessLog(
                file_id=new_file.id,
                user_id=current_user.id,
                action="created"
            )
            db.session.add(access_log)

            # Commit all changes
            db.session.commit()

            flash('File added successfully')
            return redirect(url_for('view_file', file_id=new_file.id))

    return render_template('add_file.html')

@app.route('/files/<int:file_id>')
@login_required
def view_file(file_id):
    file = File.query.get_or_404(file_id)

    # Log the file access
    access_log = AccessLog(
        file_id=file.id,
        user_id=current_user.id,
        action="viewed"
    )
    db.session.add(access_log)
    db.session.commit()

    return render_template('view_file.html', file=file)

@app.route('/files/<int:file_id>/download')
@login_required
def download_file(file_id):
    file = File.query.get_or_404(file_id)

    # Log the file access
    access_log = AccessLog(
        file_id=file.id,
        user_id=current_user.id,
        action="downloaded"
    )
    db.session.add(access_log)
    db.session.commit()

    return send_from_directory(
        app.config['UPLOAD_FOLDER'],
        file.filename,
        as_attachment=True,
        attachment_filename=file.original_filename
    )

@app.route('/files/<int:file_id>/qrcode')
@login_required
def get_qrcode(file_id):
    file = File.query.get_or_404(file_id)
    return send_from_directory(
        app.config['QR_CODE_FOLDER'],
        file.qr_code,
        as_attachment=False
    )

# Duplicate route removed

@app.route('/scan', methods=['GET', 'POST'])
@login_required
def scan_qrcode():
    if request.method == 'POST':
        # Process manual QR code input
        file_id = request.form.get('file_id')
        if file_id:
            try:
                file = File.query.get(int(file_id))
                if file:
                    # Log the file access
                    access_log = AccessLog(
                        file_id=file.id,
                        user_id=current_user.id,
                        action="manual_search"
                    )
                    db.session.add(access_log)
                    db.session.commit()

                    flash(f'File found: {file.title}', 'success')
                    return redirect(url_for('view_file', file_id=file_id))
                else:
                    flash('File not found with that ID', 'error')
            except ValueError:
                flash('Invalid file ID format', 'error')

    return render_template('scan.html')

@app.route('/api/scan', methods=['POST'])
@login_required
def api_scan_qrcode():
    data = request.json
    qr_data = data.get('qr_data')

    try:
        # Try to parse as JSON first (new format)
        import json
        try:
            qr_dict = json.loads(qr_data)
        except json.JSONDecodeError:
            # Fallback to ast.literal_eval for old format
            import ast
            qr_dict = ast.literal_eval(qr_data)

        file_id = qr_dict.get('file_id')

        if file_id:
            file = File.query.get(file_id)
            if file:
                # Log the file access
                access_log = AccessLog(
                    file_id=file.id,
                    user_id=current_user.id,
                    action="scanned"
                )
                db.session.add(access_log)
                db.session.commit()

                return jsonify({
                    'success': True,
                    'file_id': file.id,
                    'title': file.title,
                    'description': file.description,
                    'location': {
                        'rack': file.location.rack_number,
                        'row': file.location.row_number,
                        'position': file.location.position
                    },
                    'created_at': file.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'file_size': file.file_size
                })
            else:
                return jsonify({'success': False, 'error': 'File not found'})
        else:
            return jsonify({'success': False, 'error': 'Invalid QR code format'})

    except Exception as e:
        return jsonify({'success': False, 'error': f'Error parsing QR code: {str(e)}'})

@app.route('/analytics')
@login_required
def analytics():
    # Get file access statistics
    file_stats = db.session.query(
        File.title,
        db.func.count(AccessLog.id).label('access_count')
    ).join(AccessLog).group_by(File.id).all()

    # Get user access statistics
    user_stats = db.session.query(
        User.username,
        db.func.count(AccessLog.id).label('access_count')
    ).join(AccessLog).group_by(User.id).all()

    return render_template('analytics.html', file_stats=file_stats, user_stats=user_stats)

# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('500.html'), 500

# SocketIO already initialized above

# Add this route
@app.route('/collaboration')
@login_required
def collaboration():
    # Get active users and their recent activities
    active_logs = AccessLog.query.filter(
        AccessLog.timestamp >= datetime.utcnow() - timedelta(hours=1)
    ).order_by(AccessLog.timestamp.desc()).limit(20).all()

    return render_template('collaboration.html', active_logs=active_logs)

# Add this socket event
@socketio.on('file_access')
def handle_file_access(data):
    # Broadcast to all connected clients
    emit('file_activity', {
        'user': current_user.username,
        'file_id': data['file_id'],
        'file_title': data['file_title'],
        'action': data['action'],
        'timestamp': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
    }, broadcast=True)

# Duplicate error handler removed

if __name__ == '__main__':
    # Create all database tables and default users
    with app.app_context():
        db.create_all()

        # Create default users if they don't exist
        if not User.query.filter_by(username='admin').first():
            admin_user = User(username='admin', email='<EMAIL>', role='Administrator')
            admin_user.set_password('admin123')
            db.session.add(admin_user)

        if not User.query.filter_by(username='officer').first():
            officer_user = User(username='officer', email='<EMAIL>', role='Officer')
            officer_user.set_password('officer123')
            db.session.add(officer_user)

        if not User.query.filter_by(username='clerk').first():
            clerk_user = User(username='clerk', email='<EMAIL>', role='Clerk')
            clerk_user.set_password('clerk123')
            db.session.add(clerk_user)

        db.session.commit()
        print("Database initialized with default users:")
        print("Administrator: admin/admin123")
        print("Officer: officer/officer123")
        print("Clerk: clerk/clerk123")

    app.run(debug=True)