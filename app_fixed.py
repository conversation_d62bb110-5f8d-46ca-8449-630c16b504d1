#!/usr/bin/env python3
"""
Fixed version of the Taluk Office app with better error handling
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_login import Lo<PERSON><PERSON>anager, login_required, current_user, login_user, logout_user
from flask_socketio import Socket<PERSON>, emit
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import uuid
import os
import qrcode
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("🚀 Starting Taluk Office Application...")

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY') or 'taluk-office-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL') or 'sqlite:///taluk_office.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['QR_CODE_FOLDER'] = 'static/qrcodes'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

print(f"✅ Configuration loaded")
print(f"   Database: {app.config['SQLALCHEMY_DATABASE_URI']}")

# Create upload directories
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['QR_CODE_FOLDER'], exist_ok=True)
print("✅ Upload directories created")

# Import extensions
from extensions import db, login_manager, socketio

print("✅ Extensions imported")

# Configure extensions
db.init_app(app)
login_manager.init_app(app)
socketio.init_app(app, cors_allowed_origins="*")

print("✅ Extensions configured")

# Import models after db initialization
try:
    from models.user import User
    from models.file import File
    from models.location import Location
    from models.access_log import AccessLog
    print("✅ Models imported successfully")
except Exception as e:
    print(f"❌ Model import error: {e}")
    exit(1)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Basic routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user)
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid username or password', 'error')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    files = File.query.all()
    return render_template('dashboard.html', files=files)

@app.route('/files/add', methods=['GET', 'POST'])
@login_required
def add_file():
    if request.method == 'POST':
        try:
            title = request.form['title']
            description = request.form.get('description', '')
            rack_number = request.form['rack_number']
            row_number = request.form['row_number']
            position = request.form['position']
            file = request.files['file']

            if file:
                # Generate a unique filename
                filename = secure_filename(file.filename)
                unique_filename = f"{uuid.uuid4()}_{filename}"
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)

                # Save file and get size
                file.save(file_path)
                file_size = os.path.getsize(file_path)

                # Create location record
                location = Location(rack_number=rack_number, row_number=row_number, position=position)
                db.session.add(location)
                db.session.flush()

                # Create file record
                new_file = File(
                    title=title,
                    description=description,
                    filename=unique_filename,
                    original_filename=filename,
                    file_size=file_size,
                    location_id=location.id,
                    user_id=current_user.id
                )
                db.session.add(new_file)
                db.session.flush()

                # Generate QR code
                try:
                    qr_data = {
                        'file_id': new_file.id,
                        'title': title,
                        'description': description or '',
                        'location': f"Rack: {rack_number}, Row: {row_number}, Position: {position}",
                        'created_at': new_file.created_at.strftime('%Y-%m-%d %H:%M:%S')
                    }

                    qr = qrcode.QRCode(
                        version=1,
                        error_correction=qrcode.constants.ERROR_CORRECT_L,
                        box_size=10,
                        border=4,
                    )

                    qr_json_data = json.dumps(qr_data)
                    qr.add_data(qr_json_data)
                    qr.make(fit=True)

                    qr_img = qr.make_image(fill_color="black", back_color="white")
                    qr_filename = f"qr_{new_file.id}.png"
                    qr_path = os.path.join(app.config['QR_CODE_FOLDER'], qr_filename)
                    qr_img.save(qr_path)

                    new_file.qr_code = qr_filename
                    print(f"✅ QR code generated: {qr_filename}")

                except Exception as e:
                    print(f"❌ QR code generation failed: {str(e)}")
                    flash(f'File uploaded but QR code generation failed: {str(e)}', 'warning')

                db.session.commit()
                flash('File uploaded successfully!', 'success')
                return redirect(url_for('view_file', file_id=new_file.id))

        except Exception as e:
            db.session.rollback()
            flash(f'Error uploading file: {str(e)}', 'error')
            print(f"❌ File upload error: {str(e)}")

    return render_template('add_file.html')

@app.route('/files/<int:file_id>')
@login_required
def view_file(file_id):
    file = File.query.get_or_404(file_id)

    # Log the file access
    access_log = AccessLog(
        file_id=file.id,
        user_id=current_user.id,
        action="viewed"
    )
    db.session.add(access_log)
    db.session.commit()

    return render_template('view_file.html', file=file)

@app.route('/scan')
@login_required
def scan_qrcode():
    return render_template('scan.html')

@app.route('/api/scan', methods=['POST'])
@login_required
def api_scan_qrcode():
    data = request.json
    qr_data = data.get('qr_data')

    try:
        # Try to parse as JSON
        try:
            qr_dict = json.loads(qr_data)
        except json.JSONDecodeError:
            # Fallback to ast.literal_eval for old format
            import ast
            qr_dict = ast.literal_eval(qr_data)

        file_id = qr_dict.get('file_id')

        if file_id:
            file = File.query.get(file_id)
            if file:
                # Log the file access
                access_log = AccessLog(
                    file_id=file.id,
                    user_id=current_user.id,
                    action="scanned"
                )
                db.session.add(access_log)
                db.session.commit()

                return jsonify({
                    'success': True,
                    'file_id': file.id,
                    'title': file.title,
                    'description': file.description,
                    'location': {
                        'rack': file.location.rack_number,
                        'row': file.location.row_number,
                        'position': file.location.position
                    },
                    'created_at': file.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'file_size': file.file_size
                })
            else:
                return jsonify({'success': False, 'error': 'File not found'})
        else:
            return jsonify({'success': False, 'error': 'Invalid QR code format'})

    except Exception as e:
        return jsonify({'success': False, 'error': f'Error parsing QR code: {str(e)}'})

if __name__ == '__main__':
    print("🔧 Initializing database...")

    try:
        with app.app_context():
            db.create_all()
            print("✅ Database tables created")

            # Create default users if they don't exist
            if not User.query.filter_by(username='admin').first():
                admin_user = User(username='admin', email='<EMAIL>', role='Administrator')
                admin_user.set_password('admin123')
                db.session.add(admin_user)

            if not User.query.filter_by(username='officer').first():
                officer_user = User(username='officer', email='<EMAIL>', role='Officer')
                officer_user.set_password('officer123')
                db.session.add(officer_user)

            if not User.query.filter_by(username='clerk').first():
                clerk_user = User(username='clerk', email='<EMAIL>', role='Clerk')
                clerk_user.set_password('clerk123')
                db.session.add(clerk_user)

            db.session.commit()
            print("✅ Default users created")
            print("   Administrator: admin/admin123")
            print("   Officer: officer/officer123")
            print("   Clerk: clerk/clerk123")

        print("🚀 Starting Flask application...")
        app.run(debug=True, host='127.0.0.1', port=5000)

    except Exception as e:
        print(f"❌ Application startup failed: {e}")
        import traceback
        traceback.print_exc()
