-- Create database for Taluk Office File Management System
CREATE DATABASE IF NOT EXISTS taluk_office_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user for the application (optional)
-- CREATE USER 'taluk_user'@'localhost' IDENTIFIED BY 'taluk_password';
-- GRANT ALL PRIVILEGES ON taluk_office_db.* TO 'taluk_user'@'localhost';
-- FLUSH PRIVILEGES;

USE taluk_office_db;

-- Show that database is ready
SELECT 'Taluk Office Database Created Successfully!' as message;
