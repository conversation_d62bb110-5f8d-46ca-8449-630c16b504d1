#!/usr/bin/env python3
"""
Simple script to create MySQL database for Taluk Office
"""

import pymysql

def create_database():
    """Create the MySQL database"""
    try:
        # Connect to MySQL server
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='Surendar@369',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Create database
        cursor.execute("CREATE DATABASE IF NOT EXISTS taluk_office_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✅ Database 'taluk_office_db' created successfully!")
        
        # Verify database exists
        cursor.execute("SHOW DATABASES LIKE 'taluk_office_db'")
        result = cursor.fetchone()
        if result:
            print("✅ Database confirmed to exist")
        
        cursor.close()
        connection.close()
        
        print("\n🎉 MySQL database setup completed!")
        print("Database: taluk_office_db")
        print("User: root")
        print("Password: Surendar@369")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🏛️  Creating MySQL Database for Taluk Office")
    print("=" * 50)
    create_database()
