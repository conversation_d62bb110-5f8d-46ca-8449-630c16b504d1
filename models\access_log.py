from extensions import db
from datetime import datetime

class AccessLog(db.Model):
    __tablename__ = 'access_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    action = db.Column(db.String(50), nullable=False)  # created, viewed, downloaded, scanned, etc.
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Foreign keys
    file_id = db.Column(db.Integer, db.ForeignKey('files.id'))
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=True)  # Nullable for anonymous access
    
    def __repr__(self):
        return f'<AccessLog {self.action} on File {self.file_id} by User {self.user_id}>'