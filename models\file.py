from extensions import db
from datetime import datetime

class File(db.Model):
    __tablename__ = 'files'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    filename = db.Column(db.String(255), nullable=False)  # Stored filename
    original_filename = db.Column(db.String(255))  # Original filename
    file_size = db.Column(db.Integer)  # File size in bytes
    qr_code = db.Column(db.String(255))  # QR code image filename
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Foreign keys
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    location_id = db.Column(db.Integer, db.<PERSON><PERSON>('locations.id'))

    # Relationships
    user = db.relationship('User', back_populates='files')
    location = db.relationship('Location', back_populates='files')
    access_logs = db.relationship('AccessLog', backref='file', lazy='dynamic', order_by='AccessLog.timestamp.desc()')

    def __repr__(self):
        return f'<File {self.title}>'