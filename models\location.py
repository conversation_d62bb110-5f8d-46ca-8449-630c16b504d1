from extensions import db

class Location(db.Model):
    __tablename__ = 'locations'
    
    id = db.Column(db.Integer, primary_key=True)
    rack_number = db.Column(db.String(20), nullable=False)
    row_number = db.Column(db.String(20), nullable=False)
    position = db.Column(db.String(20), nullable=False)
    
    # Relationships
    files = db.relationship('File', backref='location', lazy='dynamic')
    
    def __repr__(self):
        return f'<Location Rack: {self.rack_number}, Row: {self.row_number}, Position: {self.position}>'