#!/usr/bin/env python3
"""
Reset database with new schema
"""

import pymysql
import os

def reset_database():
    """Reset the MySQL database"""
    try:
        # Connect to MySQL server
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='Surendar@369',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Drop and recreate database
        cursor.execute("DROP DATABASE IF EXISTS taluk_office_db")
        cursor.execute("CREATE DATABASE taluk_office_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✅ Database reset successfully!")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔄 Resetting Taluk Office Database...")
    if reset_database():
        print("✅ Database reset complete. You can now run the application.")
    else:
        print("❌ Database reset failed.")
