#!/usr/bin/env python3
"""
Reset MySQL database for Taluk Office
"""

import pymysql

def reset_database():
    """Drop and recreate the MySQL database"""
    try:
        # Connect to MySQL server
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='Surendar@369',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Drop database if exists
        cursor.execute("DROP DATABASE IF EXISTS taluk_office_db")
        print("✅ Dropped existing database")
        
        # Create database
        cursor.execute("CREATE DATABASE taluk_office_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✅ Created fresh database 'taluk_office_db'")
        
        cursor.close()
        connection.close()
        
        print("\n🎉 MySQL database reset completed!")
        print("Database: taluk_office_db")
        print("Ready for fresh table creation")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🏛️  Resetting MySQL Database for Taluk Office")
    print("=" * 50)
    reset_database()
