#!/usr/bin/env python3
"""
Setup script for Taluk Office MySQL Database
This script creates the MySQL database and configures the application to use it.
"""

import pymysql
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_mysql_database():
    """Create MySQL database for Taluk Office"""
    try:
        # Connect to MySQL server (without specifying database)
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='',  # Change this if you have a password
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Create database
        cursor.execute("CREATE DATABASE IF NOT EXISTS taluk_office_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✅ Database 'taluk_office_db' created successfully!")
        
        # Show databases to confirm
        cursor.execute("SHOW DATABASES LIKE 'taluk_office_db'")
        result = cursor.fetchone()
        if result:
            print("✅ Database confirmed to exist")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating database: {e}")
        print("Make sure MySQL is running and accessible")
        return False

def update_env_file():
    """Update .env file with MySQL configuration"""
    try:
        env_content = """SECRET_KEY=taluk-office-secret-key-2024
DATABASE_URL=mysql+pymysql://root:@localhost/taluk_office_db
"""
        
        with open('.env', 'w') as f:
            f.write(env_content)
        
        print("✅ .env file updated with MySQL configuration")
        return True
        
    except Exception as e:
        print(f"❌ Error updating .env file: {e}")
        return False

def main():
    """Main setup function"""
    print("🏛️  Taluk Office MySQL Setup")
    print("=" * 40)
    
    # Check if MySQL is available
    try:
        import pymysql
        print("✅ PyMySQL is installed")
    except ImportError:
        print("❌ PyMySQL not found. Installing...")
        os.system("pip install pymysql")
    
    # Create database
    if create_mysql_database():
        # Update environment file
        if update_env_file():
            print("\n🎉 Setup completed successfully!")
            print("\nNext steps:")
            print("1. Run: python app.py")
            print("2. Open: http://127.0.0.1:5000")
            print("3. Login with: admin/admin123")
        else:
            print("\n⚠️  Database created but .env update failed")
    else:
        print("\n❌ Setup failed. Please check MySQL installation and try again.")
        print("\nTroubleshooting:")
        print("- Make sure MySQL is installed and running")
        print("- Check if you can connect to MySQL with root user")
        print("- Verify MySQL is running on localhost:3306")

if __name__ == "__main__":
    main()
