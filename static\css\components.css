/* Component Styles for T-Office */

/* Modern Cards */
.modern-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-normal);
    overflow: hidden;
}

.modern-card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
}

.card-header-modern {
    background: var(--gradient-primary);
    color: var(--white);
    padding: var(--spacing-6);
    border-bottom: none;
}

.card-body-modern {
    padding: var(--spacing-6);
}

/* Modern Buttons */
.btn-modern {
    border-radius: var(--radius-lg);
    font-weight: 600;
    padding: var(--spacing-3) var(--spacing-6);
    transition: var(--transition-fast);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-primary-modern {
    background: var(--gradient-primary);
    color: var(--white);
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-success-modern {
    background: var(--gradient-success);
    color: var(--white);
}

.btn-outline-modern {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-modern:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Modern Form Controls */
.form-control-modern {
    border-radius: var(--radius-lg);
    border: 2px solid var(--gray-200);
    padding: var(--spacing-3) var(--spacing-4);
    transition: var(--transition-fast);
    background: var(--white);
}

.form-control-modern:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.form-label-modern {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-2);
}

/* Stats Cards */
.stats-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-4);
}

.stats-icon.primary {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.stats-icon.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.stats-icon.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.stats-icon.info {
    background: rgba(6, 182, 212, 0.1);
    color: var(--info-color);
}

.stats-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--spacing-1);
}

.stats-label {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* File Cards */
.file-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-normal);
    overflow: hidden;
    position: relative;
}

.file-card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-3px);
}

.file-card-header {
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.file-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    background: var(--gradient-primary);
    color: var(--white);
}

.file-title {
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
    font-size: var(--font-size-lg);
}

.file-meta {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-1);
}

.file-actions {
    display: flex;
    gap: var(--spacing-2);
}

.action-btn {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-lg);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    cursor: pointer;
}

.action-btn.view {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.action-btn.download {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.action-btn.delete {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.action-btn:hover {
    transform: scale(1.1);
}

/* QR Code Scanner */
.qr-scanner {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    position: relative;
}

.scanner-header {
    background: var(--gradient-primary);
    color: var(--white);
    padding: var(--spacing-6);
    text-align: center;
}

.scanner-body {
    padding: var(--spacing-6);
    text-align: center;
}

.camera-preview {
    width: 100%;
    max-width: 400px;
    height: 300px;
    border-radius: var(--radius-lg);
    background: var(--gray-100);
    border: 2px dashed var(--gray-300);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-4);
    position: relative;
    overflow: hidden;
}

.scanner-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-lg);
}

.scanner-overlay::before,
.scanner-overlay::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 3px solid var(--primary-color);
}

.scanner-overlay::before {
    top: -3px;
    left: -3px;
    border-right: none;
    border-bottom: none;
}

.scanner-overlay::after {
    bottom: -3px;
    right: -3px;
    border-left: none;
    border-top: none;
}

/* AR Locator */
.ar-container {
    background: var(--gray-900);
    border-radius: var(--radius-xl);
    overflow: hidden;
    position: relative;
    height: 400px;
}

.ar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(37, 99, 235, 0.1), rgba(16, 185, 129, 0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-align: center;
}

.ar-info {
    background: rgba(0, 0, 0, 0.7);
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
}

/* Voice Search */
.voice-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: none;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-xl);
    box-shadow: var(--shadow-xl);
    animation: pulse 2s infinite;
    z-index: 1000;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 99, 235, 0);
    }
}

/* Collaboration Indicators */
.user-activity {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-4);
    box-shadow: var(--shadow-sm);
    border-left: 4px solid var(--primary-color);
    margin-bottom: var(--spacing-3);
    transition: var(--transition-fast);
}

.user-activity:hover {
    box-shadow: var(--shadow-md);
    transform: translateX(5px);
}

.activity-user {
    font-weight: 600;
    color: var(--primary-color);
}

.activity-time {
    color: var(--gray-500);
    font-size: var(--font-size-sm);
}

/* Progress Bars */
.progress-modern {
    height: 8px;
    border-radius: var(--radius-full);
    background: var(--gray-200);
    overflow: hidden;
}

.progress-bar-modern {
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: var(--transition-normal);
}

/* Badges */
.badge-modern {
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.badge-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}
