/* T-Office Modern CSS Framework */

:root {
    /* Color Palette */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Modern Navigation */
.modern-nav {
    background: var(--gradient-primary) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    z-index: 1000;
}

.modern-nav .navbar-brand {
    font-weight: 700;
    font-size: var(--font-size-xl);
    color: var(--white) !important;
    text-decoration: none;
}

.brand-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}

.modern-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: var(--spacing-3) var(--spacing-4) !important;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    margin: 0 var(--spacing-1);
}

.modern-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white) !important;
    transform: translateY(-1px);
}

/* Search Container */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
    max-width: 300px;
}

.search-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white);
    border-radius: var(--radius-full);
    padding: var(--spacing-2) var(--spacing-4);
    padding-right: 45px;
    font-size: var(--font-size-sm);
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
    outline: none;
}

.search-btn {
    position: absolute;
    right: 5px;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    padding: var(--spacing-2);
    border-radius: var(--radius-full);
    transition: var(--transition-fast);
}

.search-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
}

/* User Menu */
.user-menu {
    display: flex;
    align-items: center;
    padding: var(--spacing-2) var(--spacing-3) !important;
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 140px);
    padding-top: 80px;
    background: var(--gray-50);
}

/* Modern Alerts */
.modern-alert {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--success-color);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: var(--white);
}

/* Modern Footer */
.modern-footer {
    background: var(--white);
    border-top: 1px solid var(--gray-200);
    padding: var(--spacing-6) 0;
    margin-top: auto;
}

.footer-links a {
    color: var(--gray-600);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* Utility Classes */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hover-lift {
    transition: var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-container {
        max-width: 200px;
        margin: var(--spacing-3) 0;
    }
    
    .main-content {
        padding-top: 70px;
    }
    
    .modern-nav .navbar-brand {
        font-size: var(--font-size-lg);
    }
}
