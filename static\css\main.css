/* Taluk Office Professional CSS Framework */

:root {
    /* Government Office Color Palette */
    --primary-color: #1e40af;
    --primary-dark: #1e3a8a;
    --primary-light: #3b82f6;
    --secondary-color: #475569;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Professional Gradients */
    --gradient-primary: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
    --gradient-secondary: linear-gradient(135deg, #475569 0%, #334155 100%);
    --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Government Header Styles */
.gov-header {
    background: linear-gradient(135deg, #ff9933 0%, #ffffff 50%, #138808 100%);
    border-bottom: 2px solid var(--primary-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.gov-emblem {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.gov-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.gov-subtitle {
    font-size: 1rem;
    color: var(--gray-700);
    font-weight: 500;
}

.gov-info {
    background: rgba(255, 255, 255, 0.8);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    border: 1px solid rgba(30, 64, 175, 0.2);
}

/* Professional Government Navigation */
.modern-nav {
    background: var(--white) !important;
    border-bottom: 3px solid var(--primary-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: var(--transition-normal);
    z-index: 1000;
    padding: 0.5rem 0;
}

.modern-nav .navbar-brand {
    font-weight: 700;
    font-size: var(--font-size-xl);
    color: var(--primary-color) !important;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.brand-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--white);
}

.modern-nav .nav-link {
    color: var(--gray-700) !important;
    font-weight: 500;
    padding: var(--spacing-3) var(--spacing-4) !important;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    margin: 0 var(--spacing-1);
    position: relative;
}

.modern-nav .nav-link:hover {
    background: var(--gray-100);
    color: var(--primary-color) !important;
}

.modern-nav .nav-link.active {
    color: var(--primary-color) !important;
    background: var(--gray-100);
}

.modern-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
}

/* Search Container */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
    max-width: 300px;
}

.search-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white);
    border-radius: var(--radius-full);
    padding: var(--spacing-2) var(--spacing-4);
    padding-right: 45px;
    font-size: var(--font-size-sm);
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
    outline: none;
}

.search-btn {
    position: absolute;
    right: 5px;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    padding: var(--spacing-2);
    border-radius: var(--radius-full);
    transition: var(--transition-fast);
}

.search-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
}

/* User Menu */
.user-menu {
    display: flex;
    align-items: center;
    padding: var(--spacing-2) var(--spacing-3) !important;
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    padding-top: 2rem;
    background: var(--gray-50);
}

/* Modern Alerts */
.modern-alert {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--success-color);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: var(--white);
}

/* Government Footer */
.gov-footer {
    background: var(--gray-800);
    color: var(--white);
    padding: 2rem 0 1rem;
    margin-top: auto;
    border-top: 3px solid var(--primary-color);
}

.footer-title {
    color: var(--primary-light);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.footer-text {
    color: var(--gray-300);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.footer-contact {
    color: var(--gray-400);
    font-size: 0.85rem;
    line-height: 1.6;
}

.footer-links {
    margin-bottom: 1rem;
}

.footer-link {
    color: var(--gray-300);
    text-decoration: none;
    margin: 0 0.5rem;
    font-size: 0.9rem;
    transition: var(--transition-fast);
}

.footer-link:hover {
    color: var(--primary-light);
}

.footer-copyright {
    color: var(--gray-500);
    font-size: 0.8rem;
}

/* Utility Classes */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hover-lift {
    transition: var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-container {
        max-width: 200px;
        margin: var(--spacing-3) 0;
    }

    .main-content {
        padding-top: 70px;
    }

    .modern-nav .navbar-brand {
        font-size: var(--font-size-lg);
    }
}
