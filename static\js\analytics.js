document.addEventListener('DOMContentLoaded', () => {
  // File access frequency chart
  const fileCtx = document.getElementById('fileAccessChart').getContext('2d');
  new Chart(fileCtx, {
    type: 'bar',
    data: {
      labels: fileLabels,  // Defined in the template
      datasets: [{
        label: 'Access Count',
        data: fileData,     // Defined in the template
        backgroundColor: 'rgba(54, 162, 235, 0.5)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });
  
  // User activity chart
  const userCtx = document.getElementById('userActivityChart').getContext('2d');
  new Chart(userCtx, {
    type: 'pie',
    data: {
      labels: userLabels,  // Defined in the template
      datasets: [{
        label: 'User Activity',
        data: userData,     // Defined in the template
        backgroundColor: [
          'rgba(255, 99, 132, 0.5)',
          'rgba(54, 162, 235, 0.5)',
          'rgba(255, 206, 86, 0.5)',
          'rgba(75, 192, 192, 0.5)',
          'rgba(153, 102, 255, 0.5)'
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)'
        ],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true
    }
  });
});