// T-Office Main JavaScript
// Enhanced functionality for modern file management system

class TOfficeApp {
    constructor() {
        this.socket = null;
        this.isOnline = navigator.onLine;
        this.offlineQueue = [];
        this.init();
    }

    init() {
        this.initializeSocket();
        this.setupEventListeners();
        this.initializeOfflineSupport();
        this.setupGlobalSearch();
        this.initializeNotifications();
        this.setupTheme();
    }

    // Socket.IO initialization for real-time features
    initializeSocket() {
        if (typeof io !== 'undefined') {
            this.socket = io();
            
            this.socket.on('connect', () => {
                console.log('Connected to server');
                this.processOfflineQueue();
            });
            
            this.socket.on('disconnect', () => {
                console.log('Disconnected from server');
            });
            
            this.socket.on('file_activity', (data) => {
                this.handleFileActivity(data);
            });
        }
    }

    // Global event listeners
    setupEventListeners() {
        // Loading overlay
        document.addEventListener('DOMContentLoaded', () => {
            this.hideLoading();
        });

        // Form submissions
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => {
                this.showLoading();
            });
        });

        // Network status
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.showNotification('Connection restored', 'success');
            this.processOfflineQueue();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showNotification('Working offline', 'warning');
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    // Offline support
    initializeOfflineSupport() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/static/js/service-worker.js')
                .then(registration => {
                    console.log('Service Worker registered');
                })
                .catch(error => {
                    console.log('Service Worker registration failed');
                });
        }
    }

    // Global search functionality
    setupGlobalSearch() {
        const searchInput = document.getElementById('globalSearch');
        if (searchInput) {
            let searchTimeout;
            
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.performSearch(e.target.value);
                }, 300);
            });
        }
    }

    // Notification system
    initializeNotifications() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }

    // Theme management
    setupTheme() {
        const savedTheme = localStorage.getItem('toffice-theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
    }

    // Utility methods
    showLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'flex';
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }

    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show modern-alert notification-toast`;
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            animation: slideInRight 0.3s ease;
        `;
        
        notification.innerHTML = `
            <i class="fas fa-${this.getNotificationIcon(type)} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, duration);
    }

    getNotificationIcon(type) {
        const icons = {
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'danger': 'times-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // Search functionality
    async performSearch(query) {
        if (query.length < 2) return;
        
        try {
            const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
            const results = await response.json();
            this.displaySearchResults(results);
        } catch (error) {
            console.error('Search error:', error);
        }
    }

    displaySearchResults(results) {
        // Implementation for search results dropdown
        console.log('Search results:', results);
    }

    // Real-time activity handling
    handleFileActivity(data) {
        this.showNotification(`${data.user} ${data.action} "${data.file_title}"`, 'info');
        
        // Update activity feed if present
        const activityFeed = document.querySelector('.activity-feed');
        if (activityFeed) {
            this.updateActivityFeed(data);
        }
    }

    updateActivityFeed(data) {
        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item';
        activityItem.innerHTML = `
            <div class="activity-icon ${data.action}">
                <i class="fas fa-${this.getActionIcon(data.action)}"></i>
            </div>
            <div class="activity-content">
                <p class="activity-text">${data.user} ${data.action} "${data.file_title}"</p>
                <div class="activity-time">Just now</div>
            </div>
        `;
        
        const firstActivity = document.querySelector('.activity-item');
        if (firstActivity) {
            firstActivity.parentNode.insertBefore(activityItem, firstActivity);
        }
    }

    getActionIcon(action) {
        const icons = {
            'created': 'plus',
            'viewed': 'eye',
            'scanned': 'qrcode',
            'downloaded': 'download',
            'updated': 'edit',
            'deleted': 'trash'
        };
        return icons[action] || 'file';
    }

    // Keyboard shortcuts
    handleKeyboardShortcuts(e) {
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'k':
                    e.preventDefault();
                    document.getElementById('globalSearch')?.focus();
                    break;
                case 'n':
                    e.preventDefault();
                    window.location.href = '/add_file';
                    break;
                case 's':
                    e.preventDefault();
                    window.location.href = '/scan';
                    break;
            }
        }
    }

    // Offline queue processing
    processOfflineQueue() {
        if (this.isOnline && this.offlineQueue.length > 0) {
            this.offlineQueue.forEach(action => {
                this.executeAction(action);
            });
            this.offlineQueue = [];
        }
    }

    executeAction(action) {
        // Implementation for executing queued actions
        console.log('Executing action:', action);
    }

    // Analytics tracking
    trackEvent(event, data = {}) {
        if (this.socket) {
            this.socket.emit('analytics_event', {
                event,
                data,
                timestamp: new Date().toISOString(),
                url: window.location.pathname
            });
        }
    }

    // Theme switching
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('toffice-theme', newTheme);
        
        this.showNotification(`Switched to ${newTheme} theme`, 'info');
    }
}

// Initialize the app
const tofficeApp = new TOfficeApp();

// Global utility functions
window.TOffice = {
    showLoading: () => tofficeApp.showLoading(),
    hideLoading: () => tofficeApp.hideLoading(),
    showNotification: (message, type, duration) => tofficeApp.showNotification(message, type, duration),
    trackEvent: (event, data) => tofficeApp.trackEvent(event, data),
    toggleTheme: () => tofficeApp.toggleTheme()
};

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .notification-toast {
        animation: slideInRight 0.3s ease;
    }
    
    [data-theme="dark"] {
        --gray-50: #1a1a1a;
        --gray-100: #2d2d2d;
        --white: #1e1e1e;
        --gray-800: #f1f1f1;
    }
`;
document.head.appendChild(style);
