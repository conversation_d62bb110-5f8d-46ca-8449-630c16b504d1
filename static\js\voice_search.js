const voiceSearch = {
    recognition: null,
    isListening: false,
    
    init: function() {
        if ('webkitSpeechRecognition' in window) {
            this.recognition = new webkitSpeechRecognition();
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            
            this.recognition.onresult = function(event) {
                const transcript = event.results[0][0].transcript;
                document.getElementById('search-input').value = transcript;
                document.getElementById('search-form').submit();
            };
            
            this.setupButton();
        } else {
            document.getElementById('voice-search-btn').style.display = 'none';
        }
    },
    
    setupButton: function() {
        const button = document.getElementById('voice-search-btn');
        button.addEventListener('click', () => {
            if (this.isListening) {
                this.recognition.stop();
                button.innerHTML = '<i class="fas fa-microphone"></i>';
                this.isListening = false;
            } else {
                this.recognition.start();
                button.innerHTML = '<i class="fas fa-microphone-slash"></i>';
                this.isListening = true;
            }
        });
    }
};

document.addEventListener('DOMContentLoaded', () => {
    voiceSearch.init();
});