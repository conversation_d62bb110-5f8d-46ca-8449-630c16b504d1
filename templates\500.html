{% extends "base.html" %}

{% block title %}Server Error - Taluk Office{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <div class="error-page">
                <div class="error-icon">
                    <i class="fas fa-server text-danger" style="font-size: 4rem;"></i>
                </div>
                <h1 class="error-title">500 - Server Error</h1>
                <p class="error-message">
                    An internal server error occurred. Please try again later or contact the administrator.
                </p>
                <div class="error-actions mt-4">
                    <a href="{{ url_for('index') }}" class="btn btn-primary me-3">
                        <i class="fas fa-home me-2"></i>Go Home
                    </a>
                    <button onclick="window.location.reload()" class="btn btn-outline-primary">
                        <i class="fas fa-redo me-2"></i>Try Again
                    </button>
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        If this problem persists, please contact: <EMAIL>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    padding: 4rem 2rem;
}

.error-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 2rem 0 1rem;
}

.error-message {
    font-size: 1.1rem;
    color: var(--gray-600);
    margin-bottom: 2rem;
}

.error-icon {
    margin-bottom: 2rem;
}
</style>
{% endblock %}
