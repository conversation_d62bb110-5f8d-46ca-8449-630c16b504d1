{% extends "base.html" %}

{% block title %}Analytics - T-Office{% endblock %}

{% block styles %}
<style>
.analytics-header {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.analytics-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.analytics-subtitle {
    color: var(--gray-600);
    font-size: 1.1rem;
}

.chart-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    margin-bottom: 2rem;
}

.chart-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    align-items: center;
    justify-content: between;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    flex: 1;
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    border-radius: var(--radius-md);
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    transition: var(--transition-fast);
}

.chart-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

.chart-body {
    padding: 2rem;
}

.chart-container {
    position: relative;
    height: 400px;
    margin-bottom: 1rem;
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: var(--radius-sm);
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.insight-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    border: 1px solid var(--gray-200);
    text-align: center;
    transition: var(--transition-normal);
}

.insight-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.insight-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 1.5rem;
}

.insight-icon.primary {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.insight-icon.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.insight-icon.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.insight-icon.info {
    background: rgba(6, 182, 212, 0.1);
    color: var(--info-color);
}

.insight-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.insight-label {
    color: var(--gray-600);
    font-weight: 500;
    margin-bottom: 1rem;
}

.insight-change {
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
}

.insight-change.positive {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.insight-change.negative {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.data-table {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.table-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 2rem;
}

.table-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.table-responsive {
    max-height: 400px;
    overflow-y: auto;
}

.modern-table {
    width: 100%;
    margin: 0;
}

.modern-table th {
    background: var(--gray-50);
    color: var(--gray-700);
    font-weight: 600;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    position: sticky;
    top: 0;
    z-index: 10;
}

.modern-table td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-800);
}

.modern-table tbody tr:hover {
    background: var(--gray-50);
}

.access-badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.access-badge.high {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.access-badge.medium {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.access-badge.low {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

@media (max-width: 768px) {
    .chart-container {
        height: 300px;
    }
    
    .chart-legend {
        gap: 1rem;
    }
    
    .insights-grid {
        grid-template-columns: 1fr;
    }
    
    .analytics-header {
        padding: 1rem;
    }
    
    .chart-body {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <!-- Analytics Header -->
    <div class="analytics-header">
        <h1 class="analytics-title">
            <i class="fas fa-chart-line me-3"></i>Analytics Dashboard
        </h1>
        <p class="analytics-subtitle">Comprehensive insights into your file management system</p>
    </div>
    
    <!-- Key Insights -->
    <div class="insights-grid">
        <div class="insight-card">
            <div class="insight-icon primary">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="insight-value">{{ file_stats|length or 0 }}</div>
            <div class="insight-label">Total Files</div>
            <div class="insight-change positive">
                <i class="fas fa-arrow-up me-1"></i>+12% this month
            </div>
        </div>
        
        <div class="insight-card">
            <div class="insight-icon success">
                <i class="fas fa-eye"></i>
            </div>
            <div class="insight-value">{{ file_stats|sum(attribute='access_count') or 0 }}</div>
            <div class="insight-label">Total Views</div>
            <div class="insight-change positive">
                <i class="fas fa-arrow-up me-1"></i>+8% this week
            </div>
        </div>
        
        <div class="insight-card">
            <div class="insight-icon warning">
                <i class="fas fa-users"></i>
            </div>
            <div class="insight-value">{{ user_stats|length or 0 }}</div>
            <div class="insight-label">Active Users</div>
            <div class="insight-change positive">
                <i class="fas fa-arrow-up me-1"></i>+3 new users
            </div>
        </div>
        
        <div class="insight-card">
            <div class="insight-icon info">
                <i class="fas fa-qrcode"></i>
            </div>
            <div class="insight-value">{{ file_stats|length or 0 }}</div>
            <div class="insight-label">QR Codes Generated</div>
            <div class="insight-change positive">
                <i class="fas fa-arrow-up me-1"></i>100% coverage
            </div>
        </div>
    </div>
    
    <!-- Charts Row -->
    <div class="row">
        <!-- File Access Chart -->
        <div class="col-lg-8">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-bar me-2"></i>File Access Frequency
                    </h3>
                    <div class="chart-controls">
                        <button class="chart-btn" onclick="updateChart('week')">Week</button>
                        <button class="chart-btn" onclick="updateChart('month')">Month</button>
                        <button class="chart-btn" onclick="updateChart('year')">Year</button>
                    </div>
                </div>
                <div class="chart-body">
                    <div class="chart-container">
                        <canvas id="fileAccessChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- User Activity Chart -->
        <div class="col-lg-4">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-pie me-2"></i>User Activity
                    </h3>
                </div>
                <div class="chart-body">
                    <div class="chart-container">
                        <canvas id="userActivityChart"></canvas>
                    </div>
                    <div class="chart-legend">
                        {% for user in user_stats[:5] %}
                        <div class="legend-item">
                            <div class="legend-color" style="background: hsl({{ loop.index * 60 }}, 70%, 50%);"></div>
                            <span>{{ user.username }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Data Tables Row -->
    <div class="row">
        <!-- Most Accessed Files -->
        <div class="col-lg-6">
            <div class="data-table">
                <div class="table-header">
                    <h3 class="table-title">
                        <i class="fas fa-fire me-2"></i>Most Accessed Files
                    </h3>
                </div>
                <div class="table-responsive">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>File Name</th>
                                <th>Access Count</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for file in file_stats[:10] %}
                            <tr>
                                <td>{{ file.title }}</td>
                                <td>{{ file.access_count }}</td>
                                <td>
                                    {% if file.access_count > 20 %}
                                    <span class="access-badge high">High</span>
                                    {% elif file.access_count > 10 %}
                                    <span class="access-badge medium">Medium</span>
                                    {% else %}
                                    <span class="access-badge low">Low</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="3" class="text-center text-muted">No data available</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- User Activity Table -->
        <div class="col-lg-6">
            <div class="data-table">
                <div class="table-header">
                    <h3 class="table-title">
                        <i class="fas fa-user-chart me-2"></i>User Activity Summary
                    </h3>
                </div>
                <div class="table-responsive">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Total Actions</th>
                                <th>Activity Level</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in user_stats[:10] %}
                            <tr>
                                <td>{{ user.username }}</td>
                                <td>{{ user.access_count }}</td>
                                <td>
                                    {% if user.access_count > 50 %}
                                    <span class="access-badge high">Very Active</span>
                                    {% elif user.access_count > 20 %}
                                    <span class="access-badge medium">Active</span>
                                    {% else %}
                                    <span class="access-badge low">Moderate</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="3" class="text-center text-muted">No data available</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // File Access Chart
    const fileAccessCtx = document.getElementById('fileAccessChart').getContext('2d');
    const fileAccessChart = new Chart(fileAccessCtx, {
        type: 'bar',
        data: {
            labels: [{% for file in file_stats[:10] %}'{{ file.title }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'Access Count',
                data: [{% for file in file_stats[:10] %}{{ file.access_count }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: 'rgba(37, 99, 235, 0.8)',
                borderColor: 'rgba(37, 99, 235, 1)',
                borderWidth: 1,
                borderRadius: 8,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxRotation: 45
                    }
                }
            }
        }
    });
    
    // User Activity Pie Chart
    const userActivityCtx = document.getElementById('userActivityChart').getContext('2d');
    const userActivityChart = new Chart(userActivityCtx, {
        type: 'doughnut',
        data: {
            labels: [{% for user in user_stats[:5] %}'{{ user.username }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for user in user_stats[:5] %}{{ user.access_count }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: [
                    'rgba(37, 99, 235, 0.8)',
                    'rgba(16, 185, 129, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(239, 68, 68, 0.8)',
                    'rgba(6, 182, 212, 0.8)'
                ],
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            cutout: '60%'
        }
    });
    
    // Store chart references globally
    window.fileAccessChart = fileAccessChart;
    window.userActivityChart = userActivityChart;
}

function updateChart(period) {
    // Simulate data update based on period
    console.log('Updating chart for period:', period);
    
    // In a real application, you would fetch new data from the server
    // For now, we'll just update the chart title or add some visual feedback
    
    // Add visual feedback
    const buttons = document.querySelectorAll('.chart-btn');
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    // Track analytics event
    if (window.TOffice && window.TOffice.trackEvent) {
        window.TOffice.trackEvent('analytics_filter', { period });
    }
}

// Add some CSS for active button state
const style = document.createElement('style');
style.textContent = `
    .chart-btn.active {
        background: rgba(255, 255, 255, 0.4) !important;
        font-weight: 600;
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
