{% extends "base.html" %}

{% block title %}Dashboard - Taluk Office{% endblock %}

{% block styles %}
<style>
.dashboard-header {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.welcome-text {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    color: var(--gray-600);
    font-size: 1.1rem;
}

.quick-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1.5rem;
}

.quick-action-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.recent-files {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.section-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 2rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.files-list {
    padding: 0;
    margin: 0;
    list-style: none;
}

.file-item {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--gray-200);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: between;
}

.file-item:hover {
    background: var(--gray-50);
}

.file-item:last-child {
    border-bottom: none;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
    font-size: 1.1rem;
}

.file-meta {
    color: var(--gray-600);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.file-actions {
    display: flex;
    gap: 0.5rem;
}

.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--gray-500);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.activity-feed {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    margin-top: 2rem;
}

.activity-item {
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.activity-icon.created {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.activity-icon.viewed {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.activity-icon.scanned {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.activity-content {
    flex: 1;
}

.activity-text {
    margin: 0;
    color: var(--gray-800);
    font-weight: 500;
}

.activity-time {
    color: var(--gray-500);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

@media (max-width: 992px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="welcome-text">Welcome, {{ current_user.username }}</h1>
                <p class="welcome-subtitle">Taluk Office Digital File Management Dashboard</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="quick-actions">
                    <a href="{{ url_for('add_file') }}" class="quick-action-btn">
                        <i class="fas fa-file-plus"></i>Add Document
                    </a>
                    <a href="{{ url_for('scan_qrcode') }}" class="quick-action-btn">
                        <i class="fas fa-qrcode"></i>QR Scanner
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="stats-grid">
        <div class="stats-card">
            <div class="stats-icon primary">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="stats-number">{{ files|length }}</div>
            <div class="stats-label">Total Documents</div>
        </div>

        <div class="stats-card">
            <div class="stats-icon success">
                <i class="fas fa-eye"></i>
            </div>
            <div class="stats-number">{{ files|selectattr('access_logs')|list|length }}</div>
            <div class="stats-label">Documents Processed</div>
        </div>

        <div class="stats-card">
            <div class="stats-icon warning">
                <i class="fas fa-qrcode"></i>
            </div>
            <div class="stats-number">{{ files|selectattr('qr_code')|list|length }}</div>
            <div class="stats-label">QR Codes Generated</div>
        </div>

        <div class="stats-card">
            <div class="stats-icon info">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-number">{{ moment().format('HH:mm') if moment else 'N/A' }}</div>
            <div class="stats-label">Last Activity</div>
        </div>
    </div>

    <!-- Dashboard Grid -->
    <div class="dashboard-grid">
        <!-- Recent Files -->
        <div class="recent-files">
            <h2 class="section-header">
                <i class="fas fa-file-alt me-2"></i>Recent Files
            </h2>

            {% if files %}
            <ul class="files-list">
                {% for file in files[:5] %}
                <li class="file-item">
                    <div class="file-info">
                        <div class="file-name">{{ file.title }}</div>
                        <div class="file-meta">
                            <span><i class="fas fa-map-marker-alt me-1"></i>Rack {{ file.location.rack_number }}, Row {{ file.location.row_number }}</span>
                            <span><i class="fas fa-calendar me-1"></i>{{ file.created_at.strftime('%Y-%m-%d') }}</span>
                        </div>
                    </div>
                    <div class="file-actions">
                        <a href="{{ url_for('view_file', file_id=file.id) }}" class="action-btn view" title="View File">
                            <i class="fas fa-eye"></i>
                        </a>
                        <button class="action-btn download" title="Download QR Code">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </li>
                {% endfor %}
            </ul>
            {% else %}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-folder-open"></i>
                </div>
                <h4>No files yet</h4>
                <p>Start by adding your first file to the system.</p>
                <a href="{{ url_for('add_file') }}" class="btn btn-primary-modern">
                    <i class="fas fa-plus me-2"></i>Add Your First File
                </a>
            </div>
            {% endif %}
        </div>

        <!-- Activity Feed -->
        <div class="activity-feed">
            <h2 class="section-header">
                <i class="fas fa-activity me-2"></i>Recent Activity
            </h2>

            <div class="activity-item">
                <div class="activity-icon created">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="activity-content">
                    <p class="activity-text">File "Project Report.pdf" was created</p>
                    <div class="activity-time">2 hours ago</div>
                </div>
            </div>

            <div class="activity-item">
                <div class="activity-icon viewed">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="activity-content">
                    <p class="activity-text">File "Meeting Notes.docx" was viewed</p>
                    <div class="activity-time">4 hours ago</div>
                </div>
            </div>

            <div class="activity-item">
                <div class="activity-icon scanned">
                    <i class="fas fa-qrcode"></i>
                </div>
                <div class="activity-content">
                    <p class="activity-text">QR code scanned for "Budget 2024.xlsx"</p>
                    <div class="activity-time">6 hours ago</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Add real-time updates using Socket.IO
const socket = io();

socket.on('file_activity', function(data) {
    // Add new activity to the feed
    const activityFeed = document.querySelector('.activity-feed');
    const newActivity = document.createElement('div');
    newActivity.className = 'activity-item';
    newActivity.innerHTML = `
        <div class="activity-icon ${data.action}">
            <i class="fas fa-${getActionIcon(data.action)}"></i>
        </div>
        <div class="activity-content">
            <p class="activity-text">${data.user} ${data.action} "${data.file_title}"</p>
            <div class="activity-time">Just now</div>
        </div>
    `;

    // Insert at the beginning
    const firstActivity = activityFeed.querySelector('.activity-item');
    if (firstActivity) {
        firstActivity.parentNode.insertBefore(newActivity, firstActivity);
    }

    // Remove last item if more than 5
    const activities = activityFeed.querySelectorAll('.activity-item');
    if (activities.length > 5) {
        activities[activities.length - 1].remove();
    }
});

function getActionIcon(action) {
    const icons = {
        'created': 'plus',
        'viewed': 'eye',
        'scanned': 'qrcode',
        'downloaded': 'download'
    };
    return icons[action] || 'file';
}

// Add smooth animations
document.addEventListener('DOMContentLoaded', function() {
    // Animate stats cards
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Animate file items
    const fileItems = document.querySelectorAll('.file-item');
    fileItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(-20px)';

        setTimeout(() => {
            item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
        }, 500 + (index * 100));
    });
});
</script>
{% endblock %}
