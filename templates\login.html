{% extends "base.html" %}

{% block title %}Login - Taluk Office Digital System{% endblock %}

{% block styles %}
<style>
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 3rem;
    width: 100%;
    max-width: 450px;
    position: relative;
    z-index: 2;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-logo {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 1.5rem;
}

.login-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: var(--gray-600);
    font-size: 1rem;
}

.form-floating-modern {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-floating-modern .form-control {
    height: 60px;
    border-radius: var(--radius-lg);
    border: 2px solid var(--gray-200);
    padding: 1rem;
    font-size: 1rem;
    transition: var(--transition-fast);
    background: var(--white);
}

.form-floating-modern .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.form-floating-modern label {
    position: absolute;
    top: 50%;
    left: 1rem;
    transform: translateY(-50%);
    color: var(--gray-500);
    font-size: 1rem;
    transition: var(--transition-fast);
    pointer-events: none;
    background: var(--white);
    padding: 0 0.5rem;
}

.form-floating-modern .form-control:focus + label,
.form-floating-modern .form-control:not(:placeholder-shown) + label {
    top: 0;
    font-size: 0.875rem;
    color: var(--primary-color);
    font-weight: 600;
}

.login-btn {
    width: 100%;
    height: 60px;
    background: var(--gradient-primary);
    border: none;
    border-radius: var(--radius-lg);
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.login-btn:hover::before {
    left: 100%;
}

.divider {
    text-align: center;
    margin: 2rem 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gray-200);
}

.divider span {
    background: var(--white);
    padding: 0 1rem;
    color: var(--gray-500);
    font-size: 0.875rem;
}

.demo-login {
    background: rgba(16, 185, 129, 0.1);
    border: 2px solid var(--success-color);
    color: var(--success-color);
    border-radius: var(--radius-lg);
    padding: 1rem;
    text-align: center;
    margin-top: 1.5rem;
}

.demo-credentials {
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.shape:nth-child(1) {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape:nth-child(2) {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.shape:nth-child(3) {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@media (max-width: 768px) {
    .login-card {
        margin: 1rem;
        padding: 2rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="login-card">
                    <div class="login-header">
                        <div class="login-logo">
                            <i class="fas fa-building"></i>
                        </div>
                        <h1 class="login-title">Taluk Office Login</h1>
                        <p class="login-subtitle">Government of Karnataka - Digital File Management System</p>
                    </div>

                    <form method="POST" id="loginForm">
                        <div class="form-floating-modern">
                            <input type="text" class="form-control" id="username" name="username"
                                   placeholder="Username" required>
                            <label for="username">Username</label>
                        </div>

                        <div class="form-floating-modern">
                            <input type="password" class="form-control" id="password" name="password"
                                   placeholder="Password" required>
                            <label for="password">Password</label>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember">
                                <label class="form-check-label" for="remember">
                                    Remember me
                                </label>
                            </div>
                            <a href="#" class="text-decoration-none">Forgot password?</a>
                        </div>

                        <button type="submit" class="login-btn">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        </button>
                    </form>

                    <div class="divider">
                        <span>or</span>
                    </div>

                    <div class="demo-login">
                        <strong>Official Login Credentials</strong>
                        <div class="demo-credentials">
                            <small><strong>Administrator:</strong> admin | password: admin123</small><br>
                            <small><strong>Officer:</strong> officer | password: officer123</small><br>
                            <small><strong>Clerk:</strong> clerk | password: clerk123</small>
                        </div>
                        <button type="button" class="btn btn-success btn-sm mt-2" onclick="fillAdminCredentials()">
                            <i class="fas fa-user-shield me-1"></i>Admin Login
                        </button>
                    </div>

                    <div class="text-center mt-3">
                        <p class="mb-0">Don't have an account? <a href="#" class="text-decoration-none">Sign up</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function fillAdminCredentials() {
    document.getElementById('username').value = 'admin';
    document.getElementById('password').value = 'admin123';

    // Trigger the label animation
    document.getElementById('username').dispatchEvent(new Event('input'));
    document.getElementById('password').dispatchEvent(new Event('input'));
}

// Add loading state to login button
document.getElementById('loginForm').addEventListener('submit', function(e) {
    const btn = this.querySelector('.login-btn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing In...';
    btn.disabled = true;
});

// Add interactive effects
document.addEventListener('DOMContentLoaded', function() {
    // Animate login card on load
    const loginCard = document.querySelector('.login-card');
    loginCard.style.opacity = '0';
    loginCard.style.transform = 'translateY(30px)';

    setTimeout(() => {
        loginCard.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        loginCard.style.opacity = '1';
        loginCard.style.transform = 'translateY(0)';
    }, 100);
});
</script>
{% endblock %}
