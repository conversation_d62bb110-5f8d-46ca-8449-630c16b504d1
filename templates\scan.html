{% extends "base.html" %}

{% block title %}QR Scanner - T-Office{% endblock %}

{% block styles %}
<style>
.scanner-container {
    max-width: 800px;
    margin: 0 auto;
}

.scanner-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-bottom: 2rem;
}

.scanner-header {
    background: var(--gradient-primary);
    color: white;
    padding: 2rem;
    text-align: center;
}

.scanner-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.scanner-subtitle {
    opacity: 0.9;
    font-size: 1.1rem;
}

.scanner-body {
    padding: 2rem;
}

.camera-container {
    position: relative;
    background: var(--gray-900);
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin-bottom: 2rem;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#video {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.scanner-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 250px;
    height: 250px;
    border: 3px solid var(--primary-color);
    border-radius: var(--radius-lg);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.2);
}

.scanner-overlay::before,
.scanner-overlay::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 30px;
    border: 4px solid var(--primary-color);
}

.scanner-overlay::before {
    top: -4px;
    left: -4px;
    border-right: none;
    border-bottom: none;
    border-radius: var(--radius-md) 0 0 0;
}

.scanner-overlay::after {
    bottom: -4px;
    right: -4px;
    border-left: none;
    border-top: none;
    border-radius: 0 0 var(--radius-md) 0;
}

.scanner-corners {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 30px;
    height: 30px;
    border: 4px solid var(--primary-color);
    border-left: none;
    border-bottom: none;
    border-radius: 0 var(--radius-md) 0 0;
}

.scanner-corners:nth-child(2) {
    top: auto;
    right: auto;
    bottom: -4px;
    left: -4px;
    border: 4px solid var(--primary-color);
    border-right: none;
    border-top: none;
    border-radius: 0 0 0 var(--radius-md);
}

.camera-placeholder {
    text-align: center;
    color: var(--gray-400);
    padding: 2rem;
}

.camera-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.scanner-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.control-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: 1rem 2rem;
    font-weight: 600;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.control-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.scan-result {
    background: var(--success-color);
    color: white;
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-bottom: 2rem;
    display: none;
}

.scan-result.show {
    display: block;
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.result-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.result-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.result-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.result-details {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    padding: 1rem;
    margin-bottom: 1rem;
}

.result-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.result-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-md);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.result-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    border-color: rgba(255, 255, 255, 0.5);
}

.manual-input {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: 2rem;
    text-align: center;
}

.manual-input h4 {
    color: var(--gray-700);
    margin-bottom: 1rem;
}

.input-group-modern {
    max-width: 400px;
    margin: 0 auto;
    display: flex;
    gap: 0.5rem;
}

.input-group-modern input {
    flex: 1;
    border-radius: var(--radius-lg);
    border: 2px solid var(--gray-200);
    padding: 1rem;
    font-size: 1rem;
}

.input-group-modern input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.ar-preview {
    background: var(--gray-900);
    border-radius: var(--radius-lg);
    height: 300px;
    display: none;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    margin-top: 2rem;
    position: relative;
    overflow: hidden;
}

.ar-preview.show {
    display: flex;
}

.ar-content {
    z-index: 2;
    position: relative;
}

.ar-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(37, 99, 235, 0.3), rgba(16, 185, 129, 0.3));
    animation: arPulse 3s ease-in-out infinite;
}

@keyframes arPulse {
    0%, 100% {
        opacity: 0.3;
    }
    50% {
        opacity: 0.6;
    }
}

@media (max-width: 768px) {
    .scanner-body {
        padding: 1rem;
    }
    
    .scanner-controls {
        flex-direction: column;
        align-items: center;
    }
    
    .control-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .result-actions {
        flex-direction: column;
    }
    
    .result-btn {
        justify-content: center;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <div class="scanner-container">
        <!-- Scanner Card -->
        <div class="scanner-card">
            <div class="scanner-header">
                <h1 class="scanner-title">
                    <i class="fas fa-qrcode me-3"></i>QR Code Scanner
                </h1>
                <p class="scanner-subtitle">Scan QR codes to instantly locate your files</p>
            </div>
            
            <div class="scanner-body">
                <!-- Camera Container -->
                <div class="camera-container" id="cameraContainer">
                    <div class="camera-placeholder" id="cameraPlaceholder">
                        <i class="fas fa-camera"></i>
                        <h4>Camera Access Required</h4>
                        <p>Click "Start Camera" to begin scanning QR codes</p>
                    </div>
                    <video id="video" style="display: none;"></video>
                    <div class="scanner-overlay" id="scannerOverlay" style="display: none;">
                        <div class="scanner-corners"></div>
                        <div class="scanner-corners"></div>
                    </div>
                </div>
                
                <!-- Scanner Controls -->
                <div class="scanner-controls">
                    <button class="control-btn" id="startCamera">
                        <i class="fas fa-play"></i>Start Camera
                    </button>
                    <button class="control-btn" id="stopCamera" style="display: none;" disabled>
                        <i class="fas fa-stop"></i>Stop Camera
                    </button>
                    <button class="control-btn" id="switchCamera" style="display: none;">
                        <i class="fas fa-sync-alt"></i>Switch Camera
                    </button>
                </div>
                
                <!-- Scan Result -->
                <div class="scan-result" id="scanResult">
                    <div class="result-header">
                        <div class="result-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <div>
                            <h3 class="result-title">File Found!</h3>
                            <p class="mb-0">QR code successfully scanned</p>
                        </div>
                    </div>
                    
                    <div class="result-details" id="resultDetails">
                        <!-- File details will be populated here -->
                    </div>
                    
                    <div class="result-actions">
                        <a href="#" class="result-btn" id="viewFileBtn">
                            <i class="fas fa-eye"></i>View File
                        </a>
                        <button class="result-btn" id="showLocationBtn">
                            <i class="fas fa-map-marker-alt"></i>Show Location
                        </button>
                        <button class="result-btn" id="scanAgainBtn">
                            <i class="fas fa-qrcode"></i>Scan Again
                        </button>
                    </div>
                </div>
                
                <!-- AR Preview -->
                <div class="ar-preview" id="arPreview">
                    <div class="ar-background"></div>
                    <div class="ar-content">
                        <h3><i class="fas fa-cube me-2"></i>AR File Locator</h3>
                        <p>Follow the virtual guide to your file location</p>
                        <div class="mt-3">
                            <strong id="arLocation">Rack A1, Row 3, Position 5</strong>
                        </div>
                    </div>
                </div>
                
                <!-- Manual Input -->
                <div class="manual-input">
                    <h4><i class="fas fa-keyboard me-2"></i>Manual QR Code Entry</h4>
                    <p class="text-muted mb-3">Enter QR code data manually if camera is not available</p>
                    
                    <form method="POST" id="manualForm">
                        <div class="input-group-modern">
                            <input type="text" name="file_id" placeholder="Enter File ID" id="manualInput">
                            <button type="submit" class="control-btn">
                                <i class="fas fa-search"></i>Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
<script>
let video, canvas, context;
let scanning = false;
let currentStream = null;
let cameras = [];
let currentCameraIndex = 0;

document.addEventListener('DOMContentLoaded', function() {
    initializeScanner();
});

async function initializeScanner() {
    video = document.getElementById('video');
    canvas = document.createElement('canvas');
    context = canvas.getContext('2d');
    
    // Get available cameras
    try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        cameras = devices.filter(device => device.kind === 'videoinput');
        
        if (cameras.length > 1) {
            document.getElementById('switchCamera').style.display = 'inline-flex';
        }
    } catch (error) {
        console.error('Error enumerating devices:', error);
    }
    
    // Event listeners
    document.getElementById('startCamera').addEventListener('click', startCamera);
    document.getElementById('stopCamera').addEventListener('click', stopCamera);
    document.getElementById('switchCamera').addEventListener('click', switchCamera);
    document.getElementById('scanAgainBtn').addEventListener('click', scanAgain);
    document.getElementById('showLocationBtn').addEventListener('click', showARLocation);
    document.getElementById('manualForm').addEventListener('submit', handleManualSubmit);
}

async function startCamera() {
    try {
        const constraints = {
            video: {
                facingMode: 'environment',
                width: { ideal: 1280 },
                height: { ideal: 720 }
            }
        };
        
        if (cameras.length > 0) {
            constraints.video.deviceId = cameras[currentCameraIndex].deviceId;
        }
        
        currentStream = await navigator.mediaDevices.getUserMedia(constraints);
        video.srcObject = currentStream;
        video.play();
        
        // Show video and hide placeholder
        document.getElementById('cameraPlaceholder').style.display = 'none';
        video.style.display = 'block';
        document.getElementById('scannerOverlay').style.display = 'block';
        
        // Update buttons
        document.getElementById('startCamera').style.display = 'none';
        document.getElementById('stopCamera').style.display = 'inline-flex';
        document.getElementById('stopCamera').disabled = false;
        
        // Start scanning
        scanning = true;
        scanQRCode();
        
    } catch (error) {
        console.error('Error accessing camera:', error);
        alert('Unable to access camera. Please check permissions.');
    }
}

function stopCamera() {
    scanning = false;
    
    if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
        currentStream = null;
    }
    
    // Hide video and show placeholder
    video.style.display = 'none';
    document.getElementById('scannerOverlay').style.display = 'none';
    document.getElementById('cameraPlaceholder').style.display = 'block';
    
    // Update buttons
    document.getElementById('startCamera').style.display = 'inline-flex';
    document.getElementById('stopCamera').style.display = 'none';
}

async function switchCamera() {
    if (cameras.length <= 1) return;
    
    currentCameraIndex = (currentCameraIndex + 1) % cameras.length;
    
    if (scanning) {
        stopCamera();
        setTimeout(startCamera, 100);
    }
}

function scanQRCode() {
    if (!scanning) return;
    
    if (video.readyState === video.HAVE_ENOUGH_DATA) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        context.drawImage(video, 0, 0, canvas.width, canvas.height);
        
        const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
        const code = jsQR(imageData.data, imageData.width, imageData.height);
        
        if (code) {
            handleQRCodeDetected(code.data);
            return;
        }
    }
    
    requestAnimationFrame(scanQRCode);
}

async function handleQRCodeDetected(qrData) {
    scanning = false;
    
    try {
        const response = await fetch('/api/scan', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ qr_data: qrData })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showScanResult(result);
        } else {
            alert('QR code not recognized: ' + result.error);
            scanning = true;
            scanQRCode();
        }
    } catch (error) {
        console.error('Error processing QR code:', error);
        alert('Error processing QR code');
        scanning = true;
        scanQRCode();
    }
}

function showScanResult(result) {
    const resultDiv = document.getElementById('scanResult');
    const detailsDiv = document.getElementById('resultDetails');
    
    detailsDiv.innerHTML = `
        <h5><i class="fas fa-file-alt me-2"></i>${result.title}</h5>
        <p class="mb-2"><strong>Location:</strong> Rack ${result.location.rack}, Row ${result.location.row}, Position ${result.location.position}</p>
        <p class="mb-0"><strong>File ID:</strong> ${result.file_id}</p>
    `;
    
    document.getElementById('viewFileBtn').href = `/files/${result.file_id}`;
    document.getElementById('arLocation').textContent = `Rack ${result.location.rack}, Row ${result.location.row}, Position ${result.location.position}`;
    
    resultDiv.classList.add('show');
    
    // Emit socket event for real-time collaboration
    if (typeof io !== 'undefined') {
        const socket = io();
        socket.emit('file_access', {
            file_id: result.file_id,
            file_title: result.title,
            action: 'scanned'
        });
    }
}

function scanAgain() {
    document.getElementById('scanResult').classList.remove('show');
    document.getElementById('arPreview').classList.remove('show');
    scanning = true;
    scanQRCode();
}

function showARLocation() {
    document.getElementById('arPreview').classList.add('show');
}

async function handleManualSubmit(e) {
    e.preventDefault();
    
    const fileId = document.getElementById('manualInput').value.trim();
    if (!fileId) return;
    
    try {
        const response = await fetch('/api/scan', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ qr_data: `{'file_id': ${fileId}}` })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showScanResult(result);
        } else {
            alert('File not found: ' + result.error);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error searching for file');
    }
}
</script>
{% endblock %}
