{% extends "base.html" %}

{% block title %}{{ file.title }} - Taluk Office{% endblock %}

{% block styles %}
<style>
.file-view-container {
    max-width: 1000px;
    margin: 0 auto;
}

.file-header {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.file-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.file-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.file-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--gray-600);
}

.meta-icon {
    width: 40px;
    height: 40px;
    background: rgba(37, 99, 235, 0.1);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
}

.file-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.action-button {
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: 1rem 2rem;
    font-weight: 600;
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.action-button.secondary {
    background: var(--white);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.action-button.secondary:hover {
    background: var(--primary-color);
    color: white;
}

.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.file-details {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.details-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 2rem;
}

.details-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.details-body {
    padding: 2rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: var(--gray-700);
}

.detail-value {
    color: var(--gray-800);
    text-align: right;
}

.qr-section {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.qr-header {
    background: var(--gradient-success);
    color: white;
    padding: 1.5rem 2rem;
    text-align: center;
}

.qr-body {
    padding: 2rem;
    text-align: center;
}

.qr-code-display {
    width: 200px;
    height: 200px;
    margin: 0 auto 1.5rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--white);
}

.qr-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.qr-btn {
    background: var(--white);
    color: var(--success-color);
    border: 2px solid var(--success-color);
    border-radius: var(--radius-lg);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.qr-btn:hover {
    background: var(--success-color);
    color: white;
}

.location-map {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    margin-bottom: 2rem;
}

.map-header {
    background: var(--gradient-secondary);
    color: white;
    padding: 1.5rem 2rem;
}

.map-body {
    padding: 2rem;
}

.location-visual {
    background: var(--gray-100);
    border-radius: var(--radius-lg);
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
}

.rack-representation {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0.5rem;
    width: 80%;
    height: 80%;
}

.rack-slot {
    background: var(--gray-300);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    color: var(--gray-600);
    transition: var(--transition-fast);
}

.rack-slot.current {
    background: var(--primary-color);
    color: white;
    animation: pulse 2s infinite;
}

.ar-button {
    background: var(--gradient-secondary);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: 1rem 2rem;
    font-weight: 600;
    width: 100%;
    transition: var(--transition-normal);
}

.ar-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.access-history {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.history-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 2rem;
}

.history-body {
    padding: 0;
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.history-item:last-child {
    border-bottom: none;
}

.history-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.history-icon.created {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.history-icon.viewed {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.history-icon.scanned {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.history-content {
    flex: 1;
}

.history-action {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.history-time {
    color: var(--gray-500);
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .file-view-container {
        margin: 0;
    }

    .file-header {
        padding: 1.5rem;
    }

    .file-title {
        font-size: 2rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .file-meta {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .file-actions {
        justify-content: center;
    }

    .action-button {
        flex: 1;
        justify-content: center;
        max-width: 200px;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .details-body,
    .qr-body,
    .map-body {
        padding: 1.5rem;
    }

    .qr-code-display {
        width: 150px;
        height: 150px;
    }

    .location-visual {
        height: 150px;
    }

    .rack-representation {
        grid-template-columns: repeat(3, 1fr);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <div class="file-view-container">
        <!-- File Header -->
        <div class="file-header">
            <h1 class="file-title">
                <div class="file-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                {{ file.title }}
            </h1>

            {% if file.description %}
            <p class="text-muted">{{ file.description }}</p>
            {% endif %}

            <div class="file-meta">
                <div class="meta-item">
                    <div class="meta-icon">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <div>
                        <strong>Created:</strong><br>
                        {{ file.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                    </div>
                </div>

                <div class="meta-item">
                    <div class="meta-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <strong>Added by:</strong><br>
                        {{ file.user.username }}
                    </div>
                </div>

                <div class="meta-item">
                    <div class="meta-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div>
                        <strong>Location:</strong><br>
                        Rack {{ file.location.rack_number }}, Row {{ file.location.row_number }}, Position {{ file.location.position }}
                    </div>
                </div>

                <div class="meta-item">
                    <div class="meta-icon">
                        <i class="fas fa-file"></i>
                    </div>
                    <div>
                        <strong>File:</strong><br>
                        {{ file.original_filename }}
                    </div>
                </div>
            </div>

            <div class="file-actions">
                <a href="{{ url_for('download_file', file_id=file.id) }}" class="action-button">
                    <i class="fas fa-download"></i>Download File
                </a>
                <a href="{{ url_for('get_qrcode', file_id=file.id) }}" class="action-button secondary" target="_blank">
                    <i class="fas fa-qrcode"></i>View QR Code
                </a>
                <button class="action-button secondary" onclick="shareFile()">
                    <i class="fas fa-share"></i>Share
                </button>
                <a href="{{ url_for('dashboard') }}" class="action-button secondary">
                    <i class="fas fa-arrow-left"></i>Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Content Grid -->
        <div class="content-grid">
            <!-- File Details -->
            <div class="file-details">
                <div class="details-header">
                    <h3 class="details-title">
                        <i class="fas fa-info-circle me-2"></i>File Details
                    </h3>
                </div>
                <div class="details-body">
                    <div class="detail-row">
                        <span class="detail-label">File ID</span>
                        <span class="detail-value">#{{ file.id }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Original Name</span>
                        <span class="detail-value">{{ file.original_filename }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Rack Number</span>
                        <span class="detail-value">{{ file.location.rack_number }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Row Number</span>
                        <span class="detail-value">{{ file.location.row_number }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Position</span>
                        <span class="detail-value">{{ file.location.position }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Last Updated</span>
                        <span class="detail-value">{{ file.updated_at.strftime('%m/%d/%Y') }}</span>
                    </div>
                </div>
            </div>

            <!-- QR Code Section -->
            <div class="qr-section">
                <div class="qr-header">
                    <h3 class="details-title">
                        <i class="fas fa-qrcode me-2"></i>QR Code
                    </h3>
                </div>
                <div class="qr-body">
                    <div class="qr-code-display">
                        {% if file.qr_code %}
                        <img src="{{ url_for('static', filename='qrcodes/' + file.qr_code) }}"
                             alt="QR Code for {{ file.title }}"
                             style="width: 100%; height: 100%; object-fit: contain;">
                        {% else %}
                        <i class="fas fa-qrcode fa-3x text-muted"></i>
                        {% endif %}
                    </div>

                    <div class="qr-actions">
                        <a href="{{ url_for('static', filename='qrcodes/' + file.qr_code) }}"
                           class="qr-btn" download>
                            <i class="fas fa-download"></i>Download QR Code
                        </a>
                        <button class="qr-btn" onclick="printQRCode()">
                            <i class="fas fa-print"></i>Print QR Code
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Location Map -->
        <div class="location-map">
            <div class="map-header">
                <h3 class="details-title">
                    <i class="fas fa-map me-2"></i>File Location Visualization
                </h3>
            </div>
            <div class="map-body">
                <div class="location-visual">
                    <div class="rack-representation">
                        {% for i in range(15) %}
                        <div class="rack-slot {% if i == 7 %}current{% endif %}">
                            {% if i == 7 %}
                            <i class="fas fa-file"></i>
                            {% else %}
                            {{ i + 1 }}
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <button class="ar-button" onclick="launchARLocator()">
                    <i class="fas fa-cube me-2"></i>Launch AR File Locator
                </button>
            </div>
        </div>

        <!-- Access History -->
        <div class="access-history">
            <div class="history-header">
                <h3 class="details-title">
                    <i class="fas fa-history me-2"></i>Access History
                </h3>
            </div>
            <div class="history-body">
                {% for log in file.access_logs.order_by(file.access_logs.c.timestamp.desc()).limit(10) %}
                <div class="history-item">
                    <div class="history-icon {{ log.action }}">
                        <i class="fas fa-{% if log.action == 'created' %}plus{% elif log.action == 'viewed' %}eye{% elif log.action == 'scanned' %}qrcode{% else %}file{% endif %}"></i>
                    </div>
                    <div class="history-content">
                        <div class="history-action">
                            File {{ log.action }}
                            {% if log.user %}by {{ log.user.username }}{% endif %}
                        </div>
                        <div class="history-time">{{ log.timestamp.strftime('%B %d, %Y at %I:%M %p') }}</div>
                    </div>
                </div>
                {% else %}
                <div class="history-item">
                    <div class="history-content text-center">
                        <div class="text-muted">No access history available</div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function shareFile() {
    if (navigator.share) {
        navigator.share({
            title: '{{ file.title }}',
            text: 'Check out this file: {{ file.title }}',
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            TOffice.showNotification('Link copied to clipboard!', 'success');
        });
    }
}

function printQRCode() {
    const qrImage = document.querySelector('.qr-code-display img');
    if (qrImage) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>QR Code - {{ file.title }}</title>
                    <style>
                        body { text-align: center; font-family: Arial, sans-serif; }
                        img { max-width: 300px; margin: 20px; }
                        h2 { margin: 20px; }
                    </style>
                </head>
                <body>
                    <h2>{{ file.title }}</h2>
                    <img src="${qrImage.src}" alt="QR Code">
                    <p>Location: Rack {{ file.location.rack_number }}, Row {{ file.location.row_number }}, Position {{ file.location.position }}</p>
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}

function launchARLocator() {
    TOffice.showNotification('AR Locator would launch here in a real implementation', 'info');

    // In a real implementation, this would:
    // 1. Check for AR capabilities
    // 2. Launch AR camera view
    // 3. Overlay location guidance

    // For demo, we'll show a modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">AR File Locator</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <div class="ar-preview show">
                        <div class="ar-background"></div>
                        <div class="ar-content">
                            <h3><i class="fas fa-cube me-2"></i>AR Navigation Active</h3>
                            <p>Follow the virtual guide to:</p>
                            <div class="mt-3">
                                <strong>Rack {{ file.location.rack_number }}, Row {{ file.location.row_number }}, Position {{ file.location.position }}</strong>
                            </div>
                            <div class="mt-4">
                                <i class="fas fa-arrow-right fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    modal.addEventListener('hidden.bs.modal', () => {
        modal.remove();
    });
}

// Track file view
if (window.TOffice && window.TOffice.trackEvent) {
    TOffice.trackEvent('file_view', {
        file_id: {{ file.id }},
        file_title: '{{ file.title }}'
    });
}

// Emit socket event for real-time collaboration
if (typeof io !== 'undefined') {
    const socket = io();
    socket.emit('file_access', {
        file_id: {{ file.id }},
        file_title: '{{ file.title }}',
        action: 'viewed'
    });
}
</script>
{% endblock %}
