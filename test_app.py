#!/usr/bin/env python3
"""
Test application startup
"""

from flask import Flask
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)

# Basic configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY') or 'test-secret-key'
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL') or 'sqlite:///test.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

@app.route('/')
def index():
    return """
    <h1>🏛️ Taluk Office Test</h1>
    <p>Application is running successfully!</p>
    <p>Database URL: {}</p>
    """.format(app.config['SQLALCHEMY_DATABASE_URI'])

@app.route('/test')
def test():
    return {"status": "success", "message": "Test endpoint working"}

if __name__ == '__main__':
    print("🚀 Starting test application...")
    print(f"Database URL: {app.config['SQLALCHEMY_DATABASE_URI']}")
    app.run(debug=True, port=5001)
