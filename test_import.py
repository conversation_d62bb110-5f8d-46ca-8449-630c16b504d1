#!/usr/bin/env python3
"""
Test imports and basic functionality
"""

try:
    print("Testing imports...")
    
    import os
    print("✅ os imported")
    
    import flask
    print("✅ flask imported")
    
    import pymysql
    print("✅ pymysql imported")
    
    import qrcode
    print("✅ qrcode imported")
    
    from models.user import User
    print("✅ User model imported")
    
    from models.file import File
    print("✅ File model imported")
    
    from models.location import Location
    print("✅ Location model imported")
    
    from models.access_log import AccessLog
    print("✅ AccessLog model imported")
    
    print("\n🎉 All imports successful!")
    
except Exception as e:
    print(f"❌ Import error: {e}")
    import traceback
    traceback.print_exc()
