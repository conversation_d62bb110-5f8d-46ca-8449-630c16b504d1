#!/usr/bin/env python3
"""
Test MySQL connection
"""

import pymysql

try:
    print("Testing MySQL connection...")
    conn = pymysql.connect(
        host='localhost',
        user='root',
        password='Surendar@369',
        database='taluk_office_db',
        charset='utf8mb4'
    )
    print("✅ MySQL connection successful")
    
    cursor = conn.cursor()
    cursor.execute("SHOW TABLES")
    tables = cursor.fetchall()
    print(f"✅ Tables found: {len(tables)}")
    for table in tables:
        print(f"   - {table[0]}")
    
    cursor.close()
    conn.close()
    print("✅ Connection closed successfully")
    
except Exception as e:
    print(f"❌ MySQL connection failed: {e}")
    import traceback
    traceback.print_exc()
