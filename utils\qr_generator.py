import qrcode
import json
import os
from flask import current_app

def generate_qr_code(data, file_id):
    """
    Generate a QR code with the given data and save it to the QR code folder.
    
    Args:
        data (dict): The data to encode in the QR code
        file_id (int): The ID of the file
        
    Returns:
        str: The filename of the generated QR code
    """
    # Convert data to JSON string
    json_data = json.dumps(data)
    
    # Create QR code
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(json_data)
    qr.make(fit=True)
    
    # Create an image from the QR Code
    img = qr.make_image(fill_color="black", back_color="white")
    
    # Save the image
    filename = f"qr_{file_id}.png"
    img_path = os.path.join(current_app.config['QR_CODE_FOLDER'], filename)
    img.save(img_path)
    
    return filename